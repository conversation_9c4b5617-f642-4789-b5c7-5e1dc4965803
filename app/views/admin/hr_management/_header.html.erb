<%
  all_onboarding_completed = current_user.documents.joins(:document_type).where(document_types: { onboarding: true }).all?(&:completed?)
  onboarding_text = all_onboarding_completed ? "Documents" : "Onboarding"

  # Use controller variables if available, otherwise calculate
  if defined?(@allowance) && @allowance
    allowance = @allowance
    holiday_days = defined?(@days) ? @days : allowance.floor
    holiday_hours = defined?(@hours) ? @hours : ((allowance - holiday_days) * 8).round
  else
    allowance = current_user.holiday_allowance
    holiday_days = allowance.floor
    holiday_hours = ((allowance - holiday_days) * 8).round
  end

  # Calculate time in lieu (placeholder - you may need to adjust based on your actual model)
  time_in_lieu_days = 4
  time_in_lieu_hours = 3

  # Calculate sick days (placeholder - you may need to adjust based on your actual model)
  sick_days = 0

  # Get task count for current user - use controller variable if available
  if defined?(@tasks) && @tasks
    task_count = @tasks.select { |task| !task.completed? }.count
  else
    task_count = Task.where(user_id: current_user.id, status: ['Uncompleted', 'Awaiting Approval']).count
  end

  # Get user role/position
  user_role = current_user.roles.first&.name || "Staff Member"
%>

<!-- User Info Header -->
<div class="border-b border-[#e5e5e5] dark:border-[#3c3c3e] bg-white dark:bg-[#2c2c2e]">
  <div class="w-full">
    <div class="relative px-6 py-2 flex items-center justify-between bg-gradient-to-b from-white to-[#f9f9f9] dark:from-[#2c2c2e] dark:to-[#252527] shadow-sm">
      <div class="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-[#0071e3]/20 to-transparent"></div>

      <!-- User Profile Section -->
      <div class="relative user-dropdown-container">
        <div class="flex items-center gap-3 cursor-pointer hover:bg-white/50 dark:hover:bg-black/10 rounded-lg px-2 py-1 transition-colors">
          <div class="relative h-10 w-10 rounded-full overflow-hidden shadow-sm ring-2 ring-white/80 dark:ring-black/20">
            <% if current_user.image.attached? %>
              <%= image_tag current_user.image, alt: "User photo", width: 40, height: 40, class: "object-cover" %>
            <% else %>
              <div class="w-full h-full bg-gradient-to-br from-[#0071e3] to-[#005bb5] flex items-center justify-center text-white font-medium text-sm">
                <%= current_user.initials %>
              </div>
            <% end %>
          </div>
          <div class="flex flex-col">
            <div class="flex items-center gap-2">
              <h2 class="text-lg font-medium text-[#1d1d1f] dark:text-white tracking-tight">
                <%= current_user.full_name_with_title %>
              </h2>
            </div>
            <p class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]"><%= user_role %></p>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 text-[#8e8e93] transition-transform duration-200 ml-1">
            <path d="m6 9 6 6 6-6"></path>
          </svg>
        </div>
      </div>

      <!-- Stats Cards -->
      <div class="flex items-center gap-4">
        <!-- Holiday Card -->
        <div class="flex flex-col items-center px-3 py-1.5 bg-white/70 dark:bg-black/20 rounded-lg backdrop-blur-sm border border-[#8c98ab]/30 dark:border-[#a1a1a6]/30 shadow-sm hover:shadow transition-all duration-200 cursor-pointer">
          <div class="flex items-baseline">
            <span class="text-xl font-semibold text-[#8c98ab] dark:text-[#a1a1a6]"><%= holiday_days %></span>
            <span class="text-xs font-medium text-[#8c98ab] dark:text-[#a1a1a6] mx-1">D</span>
            <span class="text-xl font-semibold text-[#8c98ab] dark:text-[#a1a1a6]"><%= holiday_hours %></span>
            <span class="text-xs font-medium text-[#8c98ab] dark:text-[#a1a1a6] ml-1">H</span>
          </div>
          <span class="text-xs font-semibold text-[#1d1d1f] dark:text-white">Holiday</span>
        </div>

        <!-- Time in Lieu Card -->
        <div class="flex flex-col items-center px-3 py-1.5 bg-white/70 dark:bg-black/20 rounded-lg backdrop-blur-sm border border-[#5b83b0]/30 dark:border-[#7aa1d2]/30 shadow-sm hover:shadow transition-all duration-200 cursor-pointer">
          <div class="flex items-baseline">
            <span class="text-xl font-semibold text-[#5b83b0] dark:text-[#7aa1d2]"><%= time_in_lieu_days %></span>
            <span class="text-xs font-medium text-[#5b83b0] dark:text-[#7aa1d2] mx-1">D</span>
            <span class="text-xl font-semibold text-[#5b83b0] dark:text-[#7aa1d2]"><%= time_in_lieu_hours %></span>
            <span class="text-xs font-medium text-[#5b83b0] dark:text-[#7aa1d2] ml-1">H</span>
          </div>
          <span class="text-xs font-semibold text-[#1d1d1f] dark:text-white">Time in Lieu</span>
        </div>

        <!-- Sick Days Card -->
        <div class="flex flex-col items-center px-3 py-1.5 bg-white/70 dark:bg-black/20 rounded-lg backdrop-blur-sm border border-[#e6a76b]/30 dark:border-[#f8c95c]/30 shadow-sm hover:shadow transition-all duration-200 cursor-pointer">
          <div class="flex items-baseline">
            <span class="text-xl font-semibold text-[#e6a76b] dark:text-[#f8c95c]"><%= sick_days %></span>
          </div>
          <span class="text-xs font-semibold text-[#1d1d1f] dark:text-white">Sick days</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pinned Notes Section -->
<div class="bg-[#f5f5f7] dark:bg-[#2c2c2e] border-b border-[#e5e5e5] dark:border-[#3c3c3e]">
  <div class="w-full px-6 py-2 flex items-center gap-3 overflow-x-auto hide-scrollbar">
    <!-- Sample pinned notes - you can replace with dynamic content -->
    <div class="group relative px-3.5 py-1.5 rounded-full cursor-pointer flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow transform hover:scale-[1.02] active:scale-[0.98] bg-gradient-to-r from-[#fff9eb] to-[#fff5d6] text-[#b07d1a] hover:from-[#fff5d6] hover:to-[#fff0c7] dark:from-[#8a6215]/30 dark:to-[#8a6215]/40 dark:text-[#f8c95c] ring-1 ring-[#e6b02d]/20 dark:ring-[#f8c95c]/20">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bookmark h-3.5 w-3.5 transition-transform group-hover:scale-110 group-hover:rotate-[-5deg] text-[#e6b02d] dark:text-[#f8c95c]">
        <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"></path>
      </svg>
      <span class="text-xs font-medium tracking-tight leading-tight truncate max-w-[200px] sm:max-w-[300px]">Remember to submit timesheet by Friday</span>
      <button class="absolute -right-1 -top-1 h-5 w-5 rounded-full bg-[#8e8e93]/80 text-white opacity-0 transition-all scale-90 group-hover:opacity-100 group-hover:scale-100 hover:bg-[#8e8e93] flex items-center justify-center shadow-sm hover:shadow" aria-label="Remove pinned note">
        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M18 6 6 18"></path>
          <path d="m6 6 12 12"></path>
        </svg>
      </button>
      <span class="absolute -right-1 -bottom-1 h-2 w-2 rounded-full bg-[#e6b02d] dark:bg-[#f8c95c] shadow-sm animate-pulse"></span>
    </div>

    <div class="group relative px-3.5 py-1.5 rounded-full cursor-pointer flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow transform hover:scale-[1.02] active:scale-[0.98] bg-gradient-to-r from-[#eef4ff] to-[#e4edff] text-[#3478f6] hover:from-[#e4edff] hover:to-[#dae6ff] dark:from-[#0a84ff]/20 dark:to-[#0a84ff]/30 dark:text-[#0a84ff]">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bookmark h-3.5 w-3.5 transition-transform group-hover:scale-110 group-hover:rotate-[-5deg] text-[#3478f6] dark:text-[#0a84ff]">
        <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"></path>
      </svg>
      <span class="text-xs font-medium tracking-tight leading-tight truncate max-w-[200px] sm:max-w-[300px]">Team meeting scheduled for Monday 2pm</span>
      <button class="absolute -right-1 -top-1 h-5 w-5 rounded-full bg-[#8e8e93]/80 text-white opacity-0 transition-all scale-90 group-hover:opacity-100 group-hover:scale-100 hover:bg-[#8e8e93] flex items-center justify-center shadow-sm hover:shadow" aria-label="Remove pinned note">
        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M18 6 6 18"></path>
          <path d="m6 6 12 12"></path>
        </svg>
      </button>
    </div>
  </div>
</div>

<!-- Navigation Section -->
<div class="border-b border-[#e5e5e5] dark:border-[#3c3c3e] bg-white dark:bg-[#2c2c2e]">
  <div class="w-full flex items-center justify-between overflow-x-auto hide-scrollbar px-6 py-0">
    <div class="flex overflow-x-auto hide-scrollbar">
      <% urls = [
        { name: 'Dashboard', url: '/admin/hr_management/dashboard' },
        { name: 'Calendar', url: '/admin/hr_management/holidays' },
        { name: 'Announcements', url: '/admin/hr_management/announcements' },
        { name: 'Timesheets', url: '/admin/hr_management/time_sheets' },
        { name: 'Shift Schedule', url: '/admin/hr_management/shifts' },
        { name: 'Meetings', url: '/admin/hr_management/group_meetings' },
        { name: onboarding_text, url: '/admin/hr_management/form_assignments' },
        { name: 'My Employees', url: '/admin/hr_management/users' },
      ] %>

      <% urls.each do |item| %>
        <% is_active = request.path.start_with?(item[:url]) %>
        <button class="px-4 py-2 text-sm font-medium transition-colors relative <%= is_active ? 'text-[#0071e3] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#0071e3] after:rounded-t-full' : 'text-[#6e6e73] hover:text-[#1d1d1f] dark:text-[#a1a1a6] dark:hover:text-white' %>">
          <%= link_to item[:name], item[:url], class: "!no-underline !text-inherit" %>
        </button>
      <% end %>
    </div>

    <!-- Tasks Dropdown -->
    <div class="relative notes-dropdown-container flex items-center my-1 z-40">
      <button class="group flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300 bg-gradient-to-b from-white to-[#f5f7fa] dark:from-[#3a3a3c] dark:to-[#323234] border border-[#e5e5e5]/80 dark:border-[#4c4c4e]/50 shadow-[0_1px_2px_rgba(0,0,0,0.05)] hover:shadow-[0_2px_4px_rgba(0,0,0,0.08)] backdrop-blur-sm hover:ring-1 hover:ring-[#0071e3]/20" aria-expanded="false" aria-haspopup="true" type="button">
        <span class="text-sm font-medium text-[#1d1d1f] dark:text-white tracking-tight">Tasks</span>
        <div class="flex items-center justify-center h-5 w-5 rounded-full text-xs font-medium transition-all bg-gradient-to-b from-[#0077ed] to-[#0071e3] text-white shadow-[0_1px_2px_rgba(0,0,0,0.1)] group-hover:from-[#0071e3] group-hover:to-[#006edb] group-active:scale-95">
          <%= task_count %>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 text-[#8e8e93] transition-all duration-300 group-hover:text-[#6e6e73] dark:group-hover:text-[#a1a1a6]">
          <path d="m6 9 6 6 6-6"></path>
        </svg>
      </button>
    </div>
  </div>
</div>
