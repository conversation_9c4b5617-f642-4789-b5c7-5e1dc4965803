<%
  # Calculate task count for incomplete tasks
  incomplete_tasks = @tasks.select { |task| !task.completed? }
  task_count = incomplete_tasks.count

  # Get announcement count
  announcement_count = @announcements&.count || 0

  # Get document count (placeholder since documents table shows "Nothing here yet")
  document_count = 2
%>

<div class="container-fluid" id="hr_management_dashboard">
  <div class="gutterbar">
    <%= render 'admin/hr_management/header' %>

    <div class="p-6">
      <div class="relative">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">

          <!-- Goal Tracking Card (2 columns) -->
          <div class="group rounded-2xl border border-[#e0ebfa] dark:border-[#3a4a5a] overflow-hidden transition-all duration-300 lg:col-span-2 bg-white dark:bg-[#2c2c2e] shadow-sm">
            <div class="p-5 flex flex-col md:flex-row gap-6 items-center">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-4">
                  <div class="h-10 w-10 rounded-full bg-[#f2f2f7] dark:bg-[#3a3a3c] flex items-center justify-center transition-all group-hover:bg-[#d0ffe0] dark:group-hover:bg-[#2a4a3a]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-activity h-5 w-5 text-[#34c759] dark:text-[#30d158]">
                      <path d="M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2"></path>
                    </svg>
                  </div>
                  <h3 class="text-base font-medium text-[#1d1d1f] dark:text-white">Goal Tracking</h3>
                </div>
                <div class="space-y-3">
                  <div class="p-3 rounded-xl bg-[#f0f7ff] dark:bg-[#2a3a4a] border border-[#e0ebfa] dark:border-[#3a4a5a]">
                    <div class="flex justify-between items-start mb-1">
                      <span class="text-sm font-medium text-[#1d1d1f] dark:text-white">Task Completion</span>
                      <span class="text-xs text-[#34c759] dark:text-[#30d158]">
                        <%= task_count == 0 ? '100%' : "#{(((@tasks.count - task_count).to_f / @tasks.count) * 100).round}%" %>
                      </span>
                    </div>
                    <div class="h-2 bg-[#e8f0fa] dark:bg-[#3a4a5a] rounded-full mt-2 overflow-hidden">
                      <div class="h-full bg-[#a8e6c1] dark:bg-[#7ad0a0] rounded-full" style="width: <%= task_count == 0 ? '100%' : "#{(((@tasks.count - task_count).to_f / @tasks.count) * 100).round}%" %>;"></div>
                    </div>
                  </div>
                  <div class="p-3 rounded-xl bg-[#f0f7ff] dark:bg-[#2a3a4a] border border-[#e0ebfa] dark:border-[#3a4a5a]">
                    <div class="flex justify-between items-start mb-1">
                      <span class="text-sm font-medium text-[#1d1d1f] dark:text-white">Holiday Utilization</span>
                      <span class="text-xs text-[#0071e3] dark:text-[#0a84ff]">
                        <%= @allowance > 0 ? "#{(((current_user.holiday_allowance - @allowance).to_f / current_user.holiday_allowance) * 100).round}%" : '0%' %>
                      </span>
                    </div>
                    <div class="h-2 bg-[#e8f0fa] dark:bg-[#3a4a5a] rounded-full mt-2 overflow-hidden">
                      <div class="h-full bg-[#a8c6e6] dark:bg-[#7aa0d0] rounded-full" style="width: <%= @allowance > 0 ? "#{(((current_user.holiday_allowance - @allowance).to_f / current_user.holiday_allowance) * 100).round}%" : '0%' %>;"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="w-40 h-40 relative flex-shrink-0">
                <div class="absolute inset-0 bg-gradient-to-br from-[#34c759]/10 to-[#0071e3]/10 rounded-full"></div>
                <div class="absolute inset-2 bg-white dark:bg-[#1c1c1e] rounded-full flex items-center justify-center">
                  <div class="text-center">
                    <div class="text-xl font-semibold text-[#34c759] dark:text-[#30d158]">
                      <%= task_count == 0 ? '100%' : "#{(((@tasks.count - task_count).to_f / @tasks.count) * 100).round}%" %>
                    </div>
                    <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6] mt-1">Goal Progress</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Announcements Card -->
          <div class="group rounded-2xl border border-[#e0ebfa] dark:border-[#3a4a5a] overflow-hidden transition-all duration-300">
            <div class="p-5 flex flex-col h-full">
              <div class="flex items-center gap-3 mb-4">
                <div class="h-10 w-10 rounded-full bg-[#f2f2f7] dark:bg-[#3a3a3c] flex items-center justify-center transition-all group-hover:bg-[#e0d0ff] dark:group-hover:bg-[#3a2a4a] relative">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bell h-5 w-5 text-[#5856d6] dark:text-[#5e5ce6]">
                    <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                    <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                  </svg>
                  <% if announcement_count > 0 %>
                    <div class="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center rounded-full bg-[#e53e3e] dark:bg-[#b91c1c] text-white text-xs font-medium shadow-sm">
                      <%= announcement_count %>
                    </div>
                  <% end %>
                </div>
                <h3 class="text-base font-medium text-[#1d1d1f] dark:text-white">Announcements</h3>
              </div>
              <div class="space-y-3 flex-grow">
                <% if @announcements.present? %>
                  <% @announcements.first(2).each_with_index do |announcement, index| %>
                    <div class="p-3 rounded-xl bg-[#f0f7ff] dark:bg-[#2a3a4a] border border-[#e0ebfa] dark:border-[#3a4a5a]">
                      <div class="flex justify-between items-start mb-1">
                        <span class="text-sm font-medium text-[#1d1d1f] dark:text-white"><%= announcement.title %></span>
                        <span class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">
                          <%= time_ago_in_words(announcement.created_at) %> ago
                        </span>
                      </div>
                      <p class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">
                        <%= truncate(strip_tags(announcement.content), length: 60) %>
                      </p>
                    </div>
                  <% end %>
                <% else %>
                  <div class="p-3 rounded-xl bg-[#f0f7ff] dark:bg-[#2a3a4a] border border-[#e0ebfa] dark:border-[#3a4a5a]">
                    <div class="flex justify-between items-start mb-1">
                      <span class="text-sm font-medium text-[#1d1d1f] dark:text-white">No announcements</span>
                      <span class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">--</span>
                    </div>
                    <p class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">No recent announcements to display</p>
                  </div>
                <% end %>
              </div>
              <div class="mt-4 flex justify-end">
                <%= link_to admin_hr_management_announcements_path, class: "flex items-center gap-1 text-xs font-medium text-[#5856d6] dark:text-[#5e5ce6] transition-all hover:text-[#4b4abe] dark:hover:text-[#5e5ce6]/90 group-hover:translate-x-0.5" do %>
                  View All
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right h-3 w-3">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Upcoming Schedule Card (2 columns) -->
          <div class="lg:col-span-2 rounded-2xl bg-white dark:bg-[#2c2c2e] border border-[#e0ebfa] dark:border-[#3a4a5a] overflow-hidden shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="p-6 flex flex-col h-full">
              <h3 class="text-base font-medium text-[#1d1d1f] dark:text-white mb-2">Upcoming Schedule</h3>
              <p class="text-sm text-[#6e6e73] dark:text-[#a1a1a6] mb-4">
                You have <%= incomplete_tasks.count %> pending tasks.
                <% if @newest_upcoming_holiday %>
                  Your next holiday is scheduled for <%= @newest_upcoming_holiday.start.strftime("%B %d") %>.
                <% else %>
                  No upcoming holidays scheduled.
                <% end %>
              </p>
              <div class="space-y-3 mb-4">
                <% if incomplete_tasks.any? %>
                  <% incomplete_tasks.first(4).each_with_index do |task, index| %>
                    <%
                      colors = [
                        { bg: 'bg-[#d0e6ff] dark:bg-[#3a3a6a]', text: 'text-[#0071e3] dark:text-[#0a84ff]', badge_bg: 'bg-[#d0e6ff] dark:bg-[#3a3a6a]', badge_text: 'text-[#0071e3] dark:text-[#0a84ff]' },
                        { bg: 'bg-[#d0ffe0] dark:bg-[#2a4a3a]', text: 'text-[#34c759] dark:text-[#30d158]', badge_bg: 'bg-[#d0ffe0] dark:bg-[#2a4a3a]', badge_text: 'text-[#34c759] dark:text-[#30d158]' },
                        { bg: 'bg-[#ffe8d0] dark:bg-[#4a3a2a]', text: 'text-[#ff9500] dark:text-[#ff9f0a]', badge_bg: 'bg-[#ffe8d0] dark:bg-[#4a3a2a]', badge_text: 'text-[#ff9500] dark:text-[#ff9f0a]' },
                        { bg: 'bg-[#e0d0ff] dark:bg-[#3a2a4a]', text: 'text-[#5856d6] dark:text-[#5e5ce6]', badge_bg: 'bg-[#e0d0ff] dark:bg-[#3a2a4a]', badge_text: 'text-[#5856d6] dark:text-[#5e5ce6]' }
                      ]
                      color = colors[index % colors.length]
                    %>
                    <div class="p-2.5 rounded-lg bg-[#f0f7ff] dark:bg-[#2a3a4a] border border-[#e0ebfa] dark:border-[#3a4a5a] flex justify-between items-center">
                      <div class="flex items-center gap-2">
                        <div class="h-8 w-8 rounded-full <%= color[:bg] %> flex items-center justify-center">
                          <% if task.holiday_id %>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4 <%= color[:text] %>">
                              <path d="M8 2v4"></path>
                              <path d="M16 2v4"></path>
                              <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                              <path d="M3 10h18"></path>
                            </svg>
                          <% else %>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="<%= color[:text] %>">
                              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                            </svg>
                          <% end %>
                        </div>
                        <div>
                          <div class="text-sm font-medium text-[#1d1d1f] dark:text-white">
                            <%= task.holiday_id ? 'Holiday Request' : 'Task' %>
                          </div>
                          <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">
                            <%= truncate(task.description, length: 40) %>
                          </div>
                        </div>
                      </div>
                      <span class="text-xs font-medium <%= color[:badge_text] %> <%= color[:badge_bg] %> px-2 py-0.5 rounded-full">
                        <%= task.status %>
                      </span>
                    </div>
                  <% end %>
                <% else %>
                  <div class="p-2.5 rounded-lg bg-[#f0f7ff] dark:bg-[#2a3a4a] border border-[#e0ebfa] dark:border-[#3a4a5a] flex justify-between items-center">
                    <div class="flex items-center gap-2">
                      <div class="h-8 w-8 rounded-full bg-[#d0ffe0] dark:bg-[#2a4a3a] flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#34c759] dark:text-[#30d158]">
                          <path d="M20 6 9 17l-5-5"></path>
                        </svg>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-[#1d1d1f] dark:text-white">All tasks completed!</div>
                        <div class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">Great job staying on top of your work</div>
                      </div>
                    </div>
                    <span class="text-xs font-medium text-[#34c759] dark:text-[#30d158] bg-[#d0ffe0] dark:bg-[#2a4a3a] px-2 py-0.5 rounded-full">Complete</span>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Documents Card -->
          <div class="group rounded-2xl border border-[#e0ebfa] dark:border-[#3a4a5a] overflow-hidden transition-all duration-300 bg-white dark:bg-[#2c2c2e] shadow-sm">
            <div class="p-5">
              <div class="flex items-center gap-3 mb-4">
                <div class="h-10 w-10 rounded-full bg-[#f2f2f7] dark:bg-[#3a3a3c] flex items-center justify-center transition-all group-hover:bg-[#ffe8d0] dark:group-hover:bg-[#4a3a2a] relative">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-text h-5 w-5 text-[#ff9500] dark:text-[#ff9f0a]">
                    <path d="M17 6.1H3"></path>
                    <path d="M21 12.1H3"></path>
                    <path d="M15.1 18H3"></path>
                  </svg>
                  <% if document_count > 0 %>
                    <div class="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center rounded-full bg-[#e53e3e] dark:bg-[#b91c1c] text-white text-xs font-medium shadow-sm">
                      <%= document_count %>
                    </div>
                  <% end %>
                </div>
                <h3 class="text-base font-medium text-[#1d1d1f] dark:text-white">Documents</h3>
              </div>
              <div class="space-y-3">
                <div class="p-3 rounded-xl bg-[#f0f7ff] dark:bg-[#2a3a4a] border border-[#e0ebfa] dark:border-[#3a4a5a]">
                  <div class="flex justify-between items-start mb-1">
                    <span class="text-sm font-medium text-[#1d1d1f] dark:text-white">Q2 Performance Review</span>
                    <span class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">PDF</span>
                  </div>
                  <p class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">Updated 2 days ago</p>
                </div>
                <div class="p-3 rounded-xl bg-[#f0f7ff] dark:bg-[#2a3a4a] border border-[#e0ebfa] dark:border-[#3a4a5a]">
                  <div class="flex justify-between items-start mb-1">
                    <span class="text-sm font-medium text-[#1d1d1f] dark:text-white">Staff Handbook</span>
                    <span class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">DOCX</span>
                  </div>
                  <p class="text-xs text-[#6e6e73] dark:text-[#a1a1a6]">Updated 1 week ago</p>
                </div>
              </div>
              <div class="mt-4 flex justify-end">
                <%= link_to admin_hr_management_form_assignments_path, class: "flex items-center gap-1 text-xs font-medium text-[#ff9500] dark:text-[#ff9f0a] transition-all hover:text-[#f08800] dark:hover:text-[#ff9f0a]/90 group-hover:translate-x-0.5" do %>
                  View Documents
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right h-3 w-3">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                <% end %>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
